import React, { useState, useEffect } from 'react';
import { Copy, Download, Eye, Code2, FileText, Zap } from 'lucide-react';
import { MarkdownParser } from '../utils/markdownParser';

const MarkdownConverter: React.FC = () => {
  const [markdown, setMarkdown] = useState(`# Markdown to Rich Text Converter

Welcome to the **most powerful** Markdown converter! This tool handles all standard Markdown elements and AI-generated content.

## Features

- ✅ **Headings** (H1-H6)
- ✅ **Bold** and *italic* text
- ✅ [Links](https://example.com) and images
- ✅ \`inline code\` and code blocks
- ✅ Lists (ordered and unordered)
- ✅ > Blockquotes
- ✅ Tables with alignment
- ✅ Horizontal rules

### Code Block Example

\`\`\`python
def hello_world():
    print("Hello, World!")
    return "AI-generated code works perfectly!"
\`\`\`

### List Examples

**Unordered List:**
- First item
- Second item
  - Nested item
- Third item

**Ordered List:**
1. First step
2. Second step
3. Third step

### Table Example

| Feature | Status | Priority |
|---------|--------|----------|
| Parsing | ✅ Done | High |
| Export | ✅ Done | Medium |
| Preview | ✅ Done | High |

> **Note:** This converter handles complex AI-generated Markdown including nested backticks, mixed indentation, and Grok-style responses.

---

Try editing this content to see the live conversion!`);

  const [parsedElements, setParsedElements] = useState(MarkdownParser.parse(markdown));
  const [htmlOutput, setHtmlOutput] = useState('');
  const [richTextOutput, setRichTextOutput] = useState('');
  const [activeTab, setActiveTab] = useState<'preview' | 'html' | 'richtext'>('preview');

  useEffect(() => {
    const elements = MarkdownParser.parse(markdown);
    setParsedElements(elements);
    setHtmlOutput(MarkdownParser.toHtml(elements));
    setRichTextOutput(MarkdownParser.toRichText(elements));
  }, [markdown]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const downloadFile = (content: string, filename: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderPreview = () => {
    return (
      <div className="prose prose-lg max-w-none">
        <div dangerouslySetInnerHTML={{ __html: htmlOutput }} />
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
              <Zap className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Markdown to Rich Text Converter
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transform your Markdown content into beautiful rich text with support for all standard elements, 
            code blocks, and AI-generated content formatting.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Input Section */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 flex flex-col h-[600px]">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Code2 className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-gray-800">Markdown Input</h2>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => copyToClipboard(markdown)}
                  className="flex items-center gap-1 px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </button>
                <button
                  onClick={() => downloadFile(markdown, 'markdown.md', 'text/markdown')}
                  className="flex items-center gap-1 px-3 py-1.5 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  <Download className="h-4 w-4" />
                  Download
                </button>
              </div>
            </div>
            <textarea
              value={markdown}
              onChange={(e) => setMarkdown(e.target.value)}
              className="flex-1 p-4 font-mono text-sm border-none outline-none resize-none bg-gray-50 rounded-b-xl"
              placeholder="Enter your Markdown content here..."
            />
          </div>

          {/* Output Section */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 flex flex-col h-[600px]">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Output</h2>
                </div>
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setActiveTab('preview')}
                    className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
                      activeTab === 'preview'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <Eye className="h-4 w-4" />
                    Preview
                  </button>
                  <button
                    onClick={() => setActiveTab('html')}
                    className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
                      activeTab === 'html'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <Code2 className="h-4 w-4" />
                    HTML
                  </button>
                  <button
                    onClick={() => setActiveTab('richtext')}
                    className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
                      activeTab === 'richtext'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <FileText className="h-4 w-4" />
                    Rich Text
                  </button>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    const content = activeTab === 'html' ? htmlOutput : richTextOutput;
                    copyToClipboard(content);
                  }}
                  className="flex items-center gap-1 px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </button>
                <button
                  onClick={() => {
                    const content = activeTab === 'html' ? htmlOutput : richTextOutput;
                    const extension = activeTab === 'html' ? 'html' : 'md';
                    const contentType = activeTab === 'html' ? 'text/html' : 'text/markdown';
                    downloadFile(content, `output.${extension}`, contentType);
                  }}
                  className="flex items-center gap-1 px-3 py-1.5 text-sm bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
                >
                  <Download className="h-4 w-4" />
                  Download
                </button>
              </div>
            </div>
            <div className="flex-1 p-4 overflow-auto">
              {activeTab === 'preview' && (
                <div className="h-full">
                  {renderPreview()}
                </div>
              )}
              {activeTab === 'html' && (
                <pre className="h-full bg-gray-50 p-4 rounded-lg overflow-auto">
                  <code className="text-sm text-gray-800 whitespace-pre-wrap">
                    {htmlOutput}
                  </code>
                </pre>
              )}
              {activeTab === 'richtext' && (
                <pre className="h-full bg-gray-50 p-4 rounded-lg overflow-auto">
                  <code className="text-sm text-gray-800 whitespace-pre-wrap">
                    {richTextOutput}
                  </code>
                </pre>
              )}
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">Supported Features</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Headings</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Bold & Italic</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Links & Images</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Code Blocks</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Lists & Quotes</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Tables</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
              <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
              <span className="text-xs text-gray-700">AI Content</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
              <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
              <span className="text-xs text-gray-700">Export Formats</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownConverter;