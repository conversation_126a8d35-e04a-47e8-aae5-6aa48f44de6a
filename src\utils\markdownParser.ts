export interface ParsedElement {
  type: 'heading' | 'paragraph' | 'bold' | 'italic' | 'link' | 'image' | 'list' | 'listItem' | 'blockquote' | 'code' | 'codeBlock' | 'table' | 'tableRow' | 'tableCell' | 'hr' | 'br';
  level?: number;
  content?: string;
  children?: ParsedElement[];
  url?: string;
  alt?: string;
  title?: string;
  language?: string;
  ordered?: boolean;
  headers?: string[];
  align?: string[];
}

export class MarkdownParser {
  private static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  private static parseInlineElements(text: string): ParsedElement[] {
    const elements: ParsedElement[] = [];
    let currentText = '';
    let i = 0;

    while (i < text.length) {
      // Bold with ** or __
      if ((text[i] === '*' && text[i + 1] === '*') || (text[i] === '_' && text[i + 1] === '_')) {
        if (currentText) {
          elements.push({ type: 'paragraph', content: currentText });
          currentText = '';
        }
        const delimiter = text[i];
        i += 2;
        let boldContent = '';
        while (i < text.length - 1 && !(text[i] === delimiter && text[i + 1] === delimiter)) {
          boldContent += text[i];
          i++;
        }
        if (i < text.length - 1) {
          elements.push({ type: 'bold', content: boldContent });
          i += 2;
        } else {
          currentText += delimiter + delimiter + boldContent;
        }
      }
      // Italic with * or _
      else if ((text[i] === '*' && text[i + 1] !== '*') || (text[i] === '_' && text[i + 1] !== '_')) {
        if (currentText) {
          elements.push({ type: 'paragraph', content: currentText });
          currentText = '';
        }
        const delimiter = text[i];
        i++;
        let italicContent = '';
        while (i < text.length && text[i] !== delimiter) {
          italicContent += text[i];
          i++;
        }
        if (i < text.length) {
          elements.push({ type: 'italic', content: italicContent });
          i++;
        } else {
          currentText += delimiter + italicContent;
        }
      }
      // Inline code
      else if (text[i] === '`') {
        if (currentText) {
          elements.push({ type: 'paragraph', content: currentText });
          currentText = '';
        }
        i++;
        let codeContent = '';
        while (i < text.length && text[i] !== '`') {
          codeContent += text[i];
          i++;
        }
        if (i < text.length) {
          elements.push({ type: 'code', content: codeContent });
          i++;
        } else {
          currentText += '`' + codeContent;
        }
      }
      // Links
      else if (text[i] === '[') {
        const linkStart = i;
        i++;
        let linkText = '';
        while (i < text.length && text[i] !== ']') {
          linkText += text[i];
          i++;
        }
        if (i < text.length && text[i + 1] === '(') {
          i += 2; // Skip ](
          let linkUrl = '';
          while (i < text.length && text[i] !== ')') {
            linkUrl += text[i];
            i++;
          }
          if (i < text.length) {
            if (currentText) {
              elements.push({ type: 'paragraph', content: currentText });
              currentText = '';
            }
            elements.push({ type: 'link', content: linkText, url: linkUrl });
            i++;
          } else {
            currentText += text.substring(linkStart, i);
          }
        } else {
          currentText += text.substring(linkStart, i);
        }
      }
      // Images
      else if (text[i] === '!' && text[i + 1] === '[') {
        const imgStart = i;
        i += 2;
        let altText = '';
        while (i < text.length && text[i] !== ']') {
          altText += text[i];
          i++;
        }
        if (i < text.length && text[i + 1] === '(') {
          i += 2; // Skip ](
          let imgUrl = '';
          while (i < text.length && text[i] !== ')') {
            imgUrl += text[i];
            i++;
          }
          if (i < text.length) {
            if (currentText) {
              elements.push({ type: 'paragraph', content: currentText });
              currentText = '';
            }
            elements.push({ type: 'image', alt: altText, url: imgUrl });
            i++;
          } else {
            currentText += text.substring(imgStart, i);
          }
        } else {
          currentText += text.substring(imgStart, i);
        }
      }
      else {
        currentText += text[i];
        i++;
      }
    }

    if (currentText) {
      elements.push({ type: 'paragraph', content: currentText });
    }

    return elements;
  }

  static parse(markdown: string): ParsedElement[] {
    const lines = markdown.split('\n');
    const elements: ParsedElement[] = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Empty line
      if (trimmedLine === '') {
        i++;
        continue;
      }

      // Code blocks
      if (trimmedLine.startsWith('```')) {
        const language = trimmedLine.substring(3).trim();
        i++;
        let codeContent = '';
        while (i < lines.length && !lines[i].trim().startsWith('```')) {
          codeContent += lines[i] + '\n';
          i++;
        }
        elements.push({
          type: 'codeBlock',
          content: codeContent.trimEnd(),
          language: language || 'text'
        });
        i++;
      }
      // Headings
      else if (trimmedLine.startsWith('#')) {
        const level = trimmedLine.match(/^#+/)?.[0].length || 1;
        const content = trimmedLine.substring(level).trim();
        elements.push({
          type: 'heading',
          level: Math.min(level, 6),
          content
        });
        i++;
      }
      // Blockquotes
      else if (trimmedLine.startsWith('>')) {
        const content = trimmedLine.substring(1).trim();
        elements.push({
          type: 'blockquote',
          content
        });
        i++;
      }
      // Horizontal rule
      else if (trimmedLine.match(/^[-*_]{3,}$/)) {
        elements.push({ type: 'hr' });
        i++;
      }
      // Unordered lists
      else if (trimmedLine.match(/^[-*+]\s/)) {
        const listItems: ParsedElement[] = [];
        while (i < lines.length && lines[i].trim().match(/^[-*+]\s/)) {
          const itemContent = lines[i].trim().substring(2);
          listItems.push({
            type: 'listItem',
            children: this.parseInlineElements(itemContent)
          });
          i++;
        }
        elements.push({
          type: 'list',
          ordered: false,
          children: listItems
        });
      }
      // Ordered lists
      else if (trimmedLine.match(/^\d+\.\s/)) {
        const listItems: ParsedElement[] = [];
        while (i < lines.length && lines[i].trim().match(/^\d+\.\s/)) {
          const itemContent = lines[i].trim().replace(/^\d+\.\s/, '');
          listItems.push({
            type: 'listItem',
            children: this.parseInlineElements(itemContent)
          });
          i++;
        }
        elements.push({
          type: 'list',
          ordered: true,
          children: listItems
        });
      }
      // Tables
      else if (trimmedLine.includes('|')) {
        const tableRows: ParsedElement[] = [];
        let headers: string[] = [];
        let align: string[] = [];
        
        // Parse header row
        if (trimmedLine.includes('|')) {
          headers = trimmedLine.split('|').map(cell => cell.trim()).filter(cell => cell);
          i++;
          
          // Check for alignment row
          if (i < lines.length && lines[i].trim().match(/^[\s\|:\-]+$/)) {
            align = lines[i].trim().split('|').map(cell => {
              const trimmed = cell.trim();
              if (trimmed.startsWith(':') && trimmed.endsWith(':')) return 'center';
              if (trimmed.endsWith(':')) return 'right';
              return 'left';
            });
            i++;
          }
          
          // Parse data rows
          while (i < lines.length && lines[i].trim().includes('|')) {
            const cells = lines[i].trim().split('|').map(cell => cell.trim()).filter(cell => cell);
            const rowCells: ParsedElement[] = cells.map(cell => ({
              type: 'tableCell',
              children: this.parseInlineElements(cell)
            }));
            tableRows.push({
              type: 'tableRow',
              children: rowCells
            });
            i++;
          }
          
          elements.push({
            type: 'table',
            headers,
            align,
            children: tableRows
          });
        }
      }
      // Regular paragraph
      else {
        elements.push({
          type: 'paragraph',
          children: this.parseInlineElements(trimmedLine)
        });
        i++;
      }
    }

    return elements;
  }

  static toHtml(elements: ParsedElement[]): string {
    let html = '';

    for (const element of elements) {
      switch (element.type) {
        case 'heading':
          html += `<h${element.level}>${this.escapeHtml(element.content || '')}</h${element.level}>`;
          break;
        case 'paragraph':
          if (element.children) {
            html += `<p>${this.childrenToHtml(element.children)}</p>`;
          } else {
            html += `<p>${this.escapeHtml(element.content || '')}</p>`;
          }
          break;
        case 'bold':
          html += `<strong>${this.escapeHtml(element.content || '')}</strong>`;
          break;
        case 'italic':
          html += `<em>${this.escapeHtml(element.content || '')}</em>`;
          break;
        case 'link':
          html += `<a href="${this.escapeHtml(element.url || '')}">${this.escapeHtml(element.content || '')}</a>`;
          break;
        case 'image':
          html += `<img src="${this.escapeHtml(element.url || '')}" alt="${this.escapeHtml(element.alt || '')}" />`;
          break;
        case 'code':
          html += `<code>${this.escapeHtml(element.content || '')}</code>`;
          break;
        case 'codeBlock':
          html += `<pre><code class="language-${element.language}">${this.escapeHtml(element.content || '')}</code></pre>`;
          break;
        case 'blockquote':
          html += `<blockquote>${this.escapeHtml(element.content || '')}</blockquote>`;
          break;
        case 'list':
          const tag = element.ordered ? 'ol' : 'ul';
          html += `<${tag}>${this.childrenToHtml(element.children || [])}</${tag}>`;
          break;
        case 'listItem':
          html += `<li>${this.childrenToHtml(element.children || [])}</li>`;
          break;
        case 'table':
          html += '<table>';
          if (element.headers) {
            html += '<thead><tr>';
            element.headers.forEach(header => {
              html += `<th>${this.escapeHtml(header)}</th>`;
            });
            html += '</tr></thead>';
          }
          html += '<tbody>';
          html += this.childrenToHtml(element.children || []);
          html += '</tbody></table>';
          break;
        case 'tableRow':
          html += `<tr>${this.childrenToHtml(element.children || [])}</tr>`;
          break;
        case 'tableCell':
          html += `<td>${this.childrenToHtml(element.children || [])}</td>`;
          break;
        case 'hr':
          html += '<hr />';
          break;
        case 'br':
          html += '<br />';
          break;
      }
    }

    return html;
  }

  private static childrenToHtml(children: ParsedElement[]): string {
    return children.map(child => this.toHtml([child])).join('');
  }

  static toRichText(elements: ParsedElement[]): string {
    // Convert to a rich text format that can be used in editors
    let richText = '';

    for (const element of elements) {
      switch (element.type) {
        case 'heading':
          richText += `${'#'.repeat(element.level || 1)} ${element.content || ''}\n\n`;
          break;
        case 'paragraph':
          if (element.children) {
            richText += this.childrenToRichText(element.children) + '\n\n';
          } else {
            richText += (element.content || '') + '\n\n';
          }
          break;
        case 'bold':
          richText += `**${element.content || ''}**`;
          break;
        case 'italic':
          richText += `*${element.content || ''}*`;
          break;
        case 'link':
          richText += `[${element.content || ''}](${element.url || ''})`;
          break;
        case 'image':
          richText += `![${element.alt || ''}](${element.url || ''})`;
          break;
        case 'code':
          richText += `\`${element.content || ''}\``;
          break;
        case 'codeBlock':
          richText += `\`\`\`${element.language || ''}\n${element.content || ''}\n\`\`\`\n\n`;
          break;
        case 'blockquote':
          richText += `> ${element.content || ''}\n\n`;
          break;
        case 'list':
          richText += this.listToRichText(element) + '\n';
          break;
        case 'listItem':
          // This case should not be reached directly as lists handle their items
          richText += `- ${this.childrenToRichText(element.children || [])}\n`;
          break;
        case 'table':
          // Create markdown table
          if (element.headers && element.headers.length > 0) {
            richText += `| ${element.headers.join(' | ')} |\n`;
            richText += `| ${element.headers.map(() => '---').join(' | ')} |\n`;
          }
          richText += this.childrenToRichText(element.children || []);
          richText += '\n';
          break;
        case 'tableRow':
          const cells = element.children || [];
          const cellContents = cells.map(cell => this.childrenToRichText([cell]));
          richText += `| ${cellContents.join(' | ')} |\n`;
          break;
        case 'tableCell':
          richText += this.childrenToRichText(element.children || []);
          break;
        case 'hr':
          richText += '---\n\n';
          break;
        case 'br':
          richText += '\n';
          break;
      }
    }

    return richText.trim();
  }

  private static childrenToRichText(children: ParsedElement[]): string {
    return children.map(child => this.toRichText([child])).join('');
  }

  private static listToRichText(listElement: ParsedElement): string {
    let result = '';
    const isOrdered = listElement.ordered;
    const items = listElement.children || [];

    items.forEach((item, index) => {
      const bullet = isOrdered ? `${index + 1}.` : '-';
      result += `${bullet} ${this.childrenToRichText(item.children || [])}\n`;
    });

    return result;
  }
}