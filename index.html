<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Markdown to Rich Text Converter</title>
    <style>
      /* Custom styles for better prose rendering */
      .prose {
        color: #374151;
        line-height: 1.7;
      }
      .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
        color: #1f2937;
        font-weight: 600;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
      }
      .prose h1 { font-size: 2rem; }
      .prose h2 { font-size: 1.5rem; }
      .prose h3 { font-size: 1.25rem; }
      .prose h4 { font-size: 1.125rem; }
      .prose h5 { font-size: 1rem; }
      .prose h6 { font-size: 0.875rem; }
      .prose p {
        margin-bottom: 1rem;
      }
      .prose strong {
        font-weight: 600;
        color: #1f2937;
      }
      .prose em {
        font-style: italic;
      }
      .prose code {
        background-color: #f3f4f6;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-family: 'Courier New', monospace;
        color: #dc2626;
      }
      .prose pre {
        background-color: #1f2937;
        color: #f9fafb;
        padding: 1rem;
        border-radius: 0.5rem;
        overflow-x: auto;
        margin: 1rem 0;
      }
      .prose pre code {
        background-color: transparent;
        color: inherit;
        padding: 0;
        font-size: 0.875rem;
      }
      .prose blockquote {
        border-left: 4px solid #e5e7eb;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: #6b7280;
      }
      .prose ul, .prose ol {
        padding-left: 1.5rem;
        margin-bottom: 1rem;
      }
      .prose li {
        margin-bottom: 0.5rem;
      }
      .prose a {
        color: #3b82f6;
        text-decoration: underline;
      }
      .prose a:hover {
        color: #1d4ed8;
      }
      .prose img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        margin: 1rem 0;
      }
      .prose table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
      }
      .prose th, .prose td {
        border: 1px solid #e5e7eb;
        padding: 0.5rem;
        text-align: left;
      }
      .prose th {
        background-color: #f9fafb;
        font-weight: 600;
      }
      .prose hr {
        border: none;
        height: 1px;
        background-color: #e5e7eb;
        margin: 2rem 0;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>